<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\AdminProductController;
use App\Http\Controllers\Admin\AdminDistributionController;

use App\Http\Controllers\Admin\AdminStoreController;
use App\Http\Controllers\Admin\AdminStockController;
use App\Http\Controllers\Admin\AdminStoreInventoryController;
use App\Http\Controllers\Admin\AdminProductInventoryController;
use App\Http\Controllers\Admin\AdminDownloadController;
use App\Http\Controllers\Admin\AdminProductRealizationController;
use App\Http\Controllers\Admin\AdminSupplierController;
use App\Http\Controllers\Admin\AdminSupplierDeliveryController;
use App\Http\Controllers\Admin\AdminReturnController;
use App\Http\Controllers\User\UserDashboardController;
use App\Http\Controllers\User\UserInventoryController;
use App\Http\Controllers\User\UserProfileController;
use App\Http\Controllers\User\UserDeliveryController;

use App\Http\Controllers\User\UserStockController;
use App\Http\Controllers\User\UserReportsController;
use App\Http\Controllers\User\UserProductRealizationController;
use App\Http\Controllers\User\UserProductController;

use App\Http\Controllers\User\UserAlertsController;
use App\Http\Controllers\User\UserSettingsController;
use App\Http\Controllers\User\UserReturnController;
use App\Http\Controllers\Supplier\SupplierDashboardController;
use App\Http\Controllers\Supplier\SupplierDeliveryController;
use App\Http\Controllers\Supplier\SupplierReturnController;
use App\Http\Controllers\Supplier\SupplierProfileController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.post');
});
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Cross-Dashboard Navigation Routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard-selection', [App\Http\Controllers\DashboardNavigationController::class, 'adminDashboardSelection'])->name('dashboard.selection');
    Route::get('/dashboard-return', [App\Http\Controllers\DashboardNavigationController::class, 'userDashboardReturn'])->name('dashboard.return');
    Route::post('/navigate-dashboard', [App\Http\Controllers\DashboardNavigationController::class, 'navigateToDashboard'])->name('dashboard.navigate');
});

// Admin Routes (protected by auth middleware)
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard and Main Pages
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // Static routes for navigation
    Route::get('/stores/export', [AdminStoreController::class, 'export'])->name('stores.export');
    Route::get('/stores', [AdminStoreController::class, 'index'])->name('stores');

    // Profile Routes
    Route::get('/profile', [App\Http\Controllers\Admin\AdminProfileController::class, 'show'])->name('profile');
    Route::get('/profile/edit', [App\Http\Controllers\Admin\AdminProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [App\Http\Controllers\Admin\AdminProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/password', [App\Http\Controllers\Admin\AdminProfileController::class, 'showPasswordForm'])->name('profile.password');
    Route::put('/profile/password', [App\Http\Controllers\Admin\AdminProfileController::class, 'updatePassword'])->name('profile.password.update');

    // CRUD Resource Routes
    Route::resource('users', AdminUserController::class);

    // Additional Product Routes (must be before resource route)
    Route::get('products/export', [AdminProductController::class, 'export'])->name('products.export');

    // ARCHIVED: Product creation moved to supplier dashboard
    // Route::resource('products', AdminProductController::class);

    // Keep only read operations for admin products
    Route::get('products', [AdminProductController::class, 'index'])->name('products.index');
    Route::get('products/{product}', [AdminProductController::class, 'show'])->name('products.show');
    Route::get('products/{product}/edit', [AdminProductController::class, 'edit'])->name('products.edit');
    Route::put('products/{product}', [AdminProductController::class, 'update'])->name('products.update');
    Route::delete('products/{product}', [AdminProductController::class, 'destroy'])->name('products.destroy');

    // COMMENTED OUT: Product creation routes (moved to supplier dashboard)
    // Route::get('products/create', [AdminProductController::class, 'create'])->name('products.create');
    // Route::post('products', [AdminProductController::class, 'store'])->name('products.store');

    // Additional Distribution Routes (must be before resource route)
    Route::get('distributions/export', [AdminDistributionController::class, 'export'])->name('distributions.export');
    Route::get('distributions/warehouse-stock/{product}', [AdminDistributionController::class, 'getWarehouseStock'])->name('distributions.warehouse-stock');
    Route::patch('distributions/{distribution}/status', [AdminDistributionController::class, 'updateStatus'])->name('distributions.update-status');

    Route::resource('distributions', AdminDistributionController::class);

    // Stock Management Routes
    Route::get('stock', [AdminStockController::class, 'index'])->name('stock.index');
    Route::get('stock/{product}/movements', [AdminStockController::class, 'movements'])->name('stock.movements');
    Route::get('stock/{product}/adjust', [AdminStockController::class, 'adjustForm'])->name('stock.adjust-form');
    Route::patch('stock/{product}/adjust', [AdminStockController::class, 'adjust'])->name('stock.adjust');

    // Store Inventory Management Routes
    Route::get('store-inventory', [AdminStoreInventoryController::class, 'index'])->name('store-inventory.index');
    Route::get('store-inventory/adjust-form', [AdminStoreInventoryController::class, 'adjustForm'])->name('store-inventory.adjust-form');
    Route::post('store-inventory/adjust', [AdminStoreInventoryController::class, 'adjustStock'])->name('store-inventory.adjust');
    Route::get('store-inventory/distribution-history', [AdminStoreInventoryController::class, 'getDistributionHistory'])->name('store-inventory.distribution-history');

    // Product Inventory Management Routes
    Route::get('product-inventory', [AdminProductInventoryController::class, 'index'])->name('product-inventory.index');
    Route::get('product-inventory/adjust-form', [AdminProductInventoryController::class, 'adjustForm'])->name('product-inventory.adjust-form');
    Route::post('product-inventory/adjust', [AdminProductInventoryController::class, 'adjustStock'])->name('product-inventory.adjust');
    Route::get('product-inventory/distribution-history', [AdminProductInventoryController::class, 'getDistributionHistory'])->name('product-inventory.distribution-history');

    // Download Routes
    Route::get('download', [AdminDownloadController::class, 'showForm'])->name('download.form');
    Route::post('download', [AdminDownloadController::class, 'download'])->name('download.process');

    // Product Realization Routes
    Route::get('product-realization/export', [AdminProductRealizationController::class, 'export'])->name('product-realization.export');
    Route::get('product-realization', [AdminProductRealizationController::class, 'index'])->name('product-realization.index');

    // Supplier Management Routes
    Route::get('suppliers', [AdminSupplierController::class, 'index'])->name('suppliers.index');
    Route::get('suppliers/create', [AdminSupplierController::class, 'create'])->name('suppliers.create');
    Route::post('suppliers', [AdminSupplierController::class, 'store'])->name('suppliers.store');
    Route::get('suppliers/{supplier}', [AdminSupplierController::class, 'show'])->name('suppliers.show');
    Route::delete('suppliers/{supplier}', [AdminSupplierController::class, 'destroy'])->name('suppliers.destroy');

    // Supplier Delivery Management Routes
    Route::get('supplier-deliveries', [AdminSupplierDeliveryController::class, 'index'])->name('supplier-deliveries.index');
    Route::get('supplier-deliveries/{delivery}', [AdminSupplierDeliveryController::class, 'show'])->name('supplier-deliveries.show');
    Route::put('supplier-deliveries/{delivery}/receive', [AdminSupplierDeliveryController::class, 'receive'])->name('supplier-deliveries.receive');
    Route::put('supplier-deliveries/{delivery}/cancel', [AdminSupplierDeliveryController::class, 'cancel'])->name('supplier-deliveries.cancel');

    // Return Management Routes
    Route::get('returns', [AdminReturnController::class, 'index'])->name('returns.index');
    Route::get('returns/create', [AdminReturnController::class, 'create'])->name('returns.create');
    Route::post('returns', [AdminReturnController::class, 'store'])->name('returns.store');
    Route::get('returns/{return}', [AdminReturnController::class, 'show'])->name('returns.show');
    Route::put('returns/{return}/approve', [AdminReturnController::class, 'approve'])->name('returns.approve');
    Route::put('returns/{return}/reject', [AdminReturnController::class, 'reject'])->name('returns.reject');
    Route::put('returns/{return}/complete', [AdminReturnController::class, 'complete'])->name('returns.complete');
    Route::put('returns/{return}/forward-to-supplier', [AdminReturnController::class, 'forwardToSupplier'])->name('returns.forward-to-supplier');

    // Store Return Management Routes
    Route::get('store-returns', [\App\Http\Controllers\Admin\AdminStoreReturnController::class, 'index'])->name('store-returns.index');
    Route::get('store-returns/{storeReturn}', [\App\Http\Controllers\Admin\AdminStoreReturnController::class, 'show'])->name('store-returns.show');
    Route::put('store-returns/{storeReturn}/approve', [\App\Http\Controllers\Admin\AdminStoreReturnController::class, 'approve'])->name('store-returns.approve');
    Route::put('store-returns/{storeReturn}/reject', [\App\Http\Controllers\Admin\AdminStoreReturnController::class, 'reject'])->name('store-returns.reject');
    Route::put('store-returns/{storeReturn}/forward-to-supplier', [\App\Http\Controllers\Admin\AdminStoreReturnController::class, 'forwardToSupplier'])->name('store-returns.forward-to-supplier');
    Route::put('store-returns/{storeReturn}/complete', [\App\Http\Controllers\Admin\AdminStoreReturnController::class, 'complete'])->name('store-returns.complete');

    // API Routes for Store Returns
    Route::get('products/{product}/recent-supplier', [\App\Http\Controllers\Admin\AdminStoreReturnController::class, 'getRecentSupplier'])->name('products.recent-supplier');

    // Return History Management Routes
    Route::get('return-history', [\App\Http\Controllers\Admin\AdminReturnHistoryController::class, 'index'])->name('return-history.index');
    Route::get('return-history/export', [\App\Http\Controllers\Admin\AdminReturnHistoryController::class, 'export'])->name('return-history.export');
});

// User Routes (protected by auth middleware)
Route::middleware(['auth', 'role:user'])->prefix('user')->name('user.')->group(function () {
    Route::get('/dashboard', [UserDashboardController::class, 'index'])->name('dashboard');
    Route::get('/inventory', [UserInventoryController::class, 'index'])->name('inventory');

    // Test route for mobile interactions (development only)
    Route::get('/test-mobile', function () {
        return view('user.test-mobile-interactions');
    })->name('test-mobile');

    // Delivery routes
    Route::get('/deliveries', [UserDeliveryController::class, 'index'])->name('deliveries');
    Route::get('/deliveries/{id}', [UserDeliveryController::class, 'show'])->name('deliveries.show');
    Route::patch('/deliveries/{id}/confirm', [UserDeliveryController::class, 'confirm'])->name('deliveries.confirm');



    // Settings routes (Simplified for small company)
    Route::get('/settings', [UserSettingsController::class, 'index'])->name('settings');
    Route::patch('/settings/profile', [UserSettingsController::class, 'updateProfile'])->name('settings.profile');
    Route::patch('/settings/password', [UserSettingsController::class, 'updatePassword'])->name('settings.password');
    Route::patch('/settings/notifications', [UserSettingsController::class, 'updateNotifications'])->name('settings.notifications');


    // User Profile Routes
    Route::get('/profile', [UserProfileController::class, 'show'])->name('profile');
    Route::get('/profile/edit', [UserProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [UserProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/password', [UserProfileController::class, 'showPasswordForm'])->name('profile.password');
    Route::put('/profile/password', [UserProfileController::class, 'updatePassword'])->name('profile.password.update');

    // Product Realization Routes
    Route::get('/product-realization', [UserProductRealizationController::class, 'index'])->name('product-realization.index');

    // Product Management Routes
    Route::get('/products', [UserProductController::class, 'index'])->name('products.index');
    Route::get('/products/{product}/adjust', [UserProductController::class, 'adjustForm'])->name('products.adjust.form');
    Route::post('/products/{product}/adjust', [UserProductController::class, 'adjust'])->name('products.adjust');
    Route::post('/products/{product}/set-zero', [UserProductController::class, 'setZero'])->name('products.set-zero');

    // Return Management Routes
    Route::get('/returns', [UserReturnController::class, 'index'])->name('returns.index');
    Route::get('/returns/create', [UserReturnController::class, 'create'])->name('returns.create');
    Route::post('/returns', [UserReturnController::class, 'store'])->name('returns.store');
    Route::post('/returns/from-distribution', [UserReturnController::class, 'storeFromDistribution'])->name('returns.store-from-distribution');
    Route::get('/returns/{return}', [UserReturnController::class, 'show'])->name('returns.show');
});

// Supplier Routes (protected by auth middleware)
Route::middleware(['auth', 'role:supplier_admin'])->prefix('supplier')->name('supplier.')->group(function () {
    Route::get('/dashboard', [SupplierDashboardController::class, 'index'])->name('dashboard');

    // Delivery Management Routes
    Route::get('/deliveries', [SupplierDeliveryController::class, 'index'])->name('deliveries.index');
    Route::get('/deliveries/create', [SupplierDeliveryController::class, 'create'])->name('deliveries.create');
    Route::post('/deliveries', [SupplierDeliveryController::class, 'store'])->name('deliveries.store');
    Route::get('/deliveries/{delivery}', [SupplierDeliveryController::class, 'show'])->name('deliveries.show');
    Route::delete('/deliveries/{delivery}', [SupplierDeliveryController::class, 'destroy'])->name('deliveries.destroy');

    // Product Management Routes (for supplier-centric product creation)
    Route::post('/products', [SupplierDeliveryController::class, 'createProduct'])->name('products.create');

    // Return Management Routes
    Route::get('/returns', [SupplierReturnController::class, 'index'])->name('returns.index');
    Route::get('/returns/{return}', [SupplierReturnController::class, 'show'])->name('returns.show');
    Route::put('/returns/{return}/respond', [SupplierReturnController::class, 'respond'])->name('returns.respond');
    Route::delete('/returns/{return}', [SupplierReturnController::class, 'destroy'])->name('returns.destroy');
    Route::post('/returns/{return}/resend', [SupplierReturnController::class, 'resendReturn'])->name('returns.resend');

    // Cancelled Delivery Management Routes
    Route::delete('/cancelled-deliveries/{delivery}', [SupplierReturnController::class, 'deleteCancelledDelivery'])->name('cancelled-deliveries.delete');
    Route::post('/cancelled-deliveries/{delivery}/resend', [SupplierReturnController::class, 'resendCancelledDelivery'])->name('cancelled-deliveries.resend');

    // Supplier Return History Routes
    Route::get('/return-history', [\App\Http\Controllers\Supplier\SupplierReturnHistoryController::class, 'index'])->name('return-history.index');
    Route::get('/return-history/export', [\App\Http\Controllers\Supplier\SupplierReturnHistoryController::class, 'export'])->name('return-history.export');

    // Profile Management Routes
    Route::get('/profile', [SupplierProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [SupplierProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [SupplierProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/password', [SupplierProfileController::class, 'showPasswordForm'])->name('profile.password');
    Route::put('/profile/password', [SupplierProfileController::class, 'updatePassword'])->name('profile.password.update');

    // Test Routes (for development/debugging)
    Route::get('/test-mobile', function () {
        return view('supplier.test-mobile-interactions');
    })->name('test-mobile');
});

// General dashboard route - redirects based on user role
Route::middleware('auth')->get('/dashboard', function () {
    $user = auth()->user();

    if ($user->isAdmin()) {
        return redirect()->route('admin.dashboard');
    } elseif ($user->isSupplierAdmin()) {
        return redirect()->route('supplier.dashboard');
    }

    return redirect()->route('user.dashboard');
})->name('dashboard');

// Handle registration attempts - redirect to login since no registration is available
Route::middleware('guest')->group(function () {
    Route::get('/register', function () {
        return redirect()->route('login')->with('info', 'Registrasi tidak tersedia. Silakan hubungi administrator untuk mendapatkan akun.');
    })->name('register');

    Route::post('/register', function () {
        return redirect()->route('login')->with('info', 'Registrasi tidak tersedia. Silakan hubungi administrator untuk mendapatkan akun.');
    });
});

// Fallback route for undefined paths - redirect based on authentication
Route::fallback(function () {
    if (auth()->check()) {
        $user = auth()->user();

        if ($user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        } elseif ($user->isSupplierAdmin()) {
            return redirect()->route('supplier.dashboard');
        }

        return redirect()->route('user.dashboard');
    }

    return redirect()->route('login');
});
