<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\Store;
use App\Models\Supplier;
use Carbon\Carbon;
use Illuminate\Support\Facades\Response;

class AdminReturnHistoryController extends Controller
{
    /**
     * Display a listing of return history.
     */
    public function index(Request $request)
    {
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy', 'processedBy'])
            ->processed(); // Only show processed returns (history)
        
        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);
        
        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();
        
        // Apply date filter based on processed_date
        $query->whereBetween('processed_date', [$startDate, $endDate]);
        
        // Filter by return type
        $returnType = $request->get('type', 'all');
        if ($returnType === 'store') {
            $query->storeToWarehouse(); // Returns from stores to warehouse
        } elseif ($returnType === 'supplier') {
            $query->warehouseToSupplier(); // Returns from warehouse to suppliers
        }
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('product', function ($productQuery) use ($search) {
                    $productQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('store', function ($storeQuery) use ($search) {
                    $storeQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('supplier', function ($supplierQuery) use ($search) {
                    $supplierQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('description', 'like', "%{$search}%")
                ->orWhere('processing_notes', 'like', "%{$search}%");
            });
        }
        
        // Sort by processed date (newest first)
        $query->orderBy('processed_date', 'desc');
        
        // Paginate results
        $returns = $query->paginate(15)->withQueryString();
        
        // Get statistics for the current filter
        $stats = $this->getHistoryStats($startDate, $endDate, $returnType);
        
        // Get available months for filter dropdown
        $availableMonths = $this->getAvailableMonths();
        
        return view('admin.return-history.index', compact(
            'returns',
            'stats',
            'filterMonth',
            'returnType',
            'availableMonths'
        ));
    }
    
    /**
     * Export return history to CSV.
     */
    public function export(Request $request)
    {
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy', 'processedBy'])
            ->processed();
        
        // Apply same filters as index
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();
        
        $query->whereBetween('processed_date', [$startDate, $endDate]);
        
        $returnType = $request->get('type', 'all');
        if ($returnType === 'store') {
            $query->storeToWarehouse();
        } elseif ($returnType === 'supplier') {
            $query->warehouseToSupplier();
        }
        
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('product', function ($productQuery) use ($search) {
                    $productQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('store', function ($storeQuery) use ($search) {
                    $storeQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('supplier', function ($supplierQuery) use ($search) {
                    $supplierQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('description', 'like', "%{$search}%")
                ->orWhere('processing_notes', 'like', "%{$search}%");
            });
        }
        
        $returns = $query->orderBy('processed_date', 'desc')->get();
        
        // Generate CSV content
        $csvData = [];
        $csvData[] = [
            'Tanggal Retur',
            'Tanggal Diproses',
            'Produk',
            'Asal',
            'Tujuan',
            'Jumlah',
            'Alasan',
            'Status',
            'Aksi Pemrosesan',
            'Diminta Oleh',
            'Disetujui Oleh',
            'Diproses Oleh',
            'Deskripsi',
            'Catatan Pemrosesan'
        ];
        
        foreach ($returns as $return) {
            $csvData[] = [
                $return->return_date->format('d/m/Y'),
                $return->processed_date ? $return->processed_date->format('d/m/Y') : '',
                $return->product->name,
                $return->source,
                $return->destination,
                number_format($return->quantity),
                $return->reason_in_indonesian,
                $return->status_in_indonesian,
                $return->processing_action_in_indonesian ?? '',
                $return->requestedBy->name ?? '',
                $return->approvedBy->name ?? '',
                $return->processedBy->name ?? '',
                $return->description ?? '',
                $return->processing_notes ?? ''
            ];
        }
        
        // Create CSV response
        $filename = 'riwayat-retur-' . $filterMonth . '.csv';
        
        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            foreach ($csvData as $row) {
                fputcsv($file, $row, ';'); // Use semicolon for better Excel compatibility
            }
            
            fclose($file);
        };
        
        return Response::stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
    
    /**
     * Get history statistics.
     */
    private function getHistoryStats($startDate, $endDate, $returnType)
    {
        $baseQuery = ReturnModel::processed()
            ->whereBetween('processed_date', [$startDate, $endDate]);
        
        if ($returnType === 'store') {
            $baseQuery->storeToWarehouse();
        } elseif ($returnType === 'supplier') {
            $baseQuery->warehouseToSupplier();
        }
        
        return [
            'total_processed' => $baseQuery->count(),
            'deleted' => (clone $baseQuery)->where('processing_action', 'deleted')->count(),
            'resent' => (clone $baseQuery)->where('processing_action', 'resent')->count(),
            'accepted' => (clone $baseQuery)->where('processing_action', 'accepted')->count(),
            'rejected_final' => (clone $baseQuery)->where('processing_action', 'rejected_final')->count(),
        ];
    }
    
    /**
     * Get available months for filtering.
     */
    private function getAvailableMonths()
    {
        $months = ReturnModel::processed()
            ->selectRaw('DATE_FORMAT(processed_date, "%Y-%m") as month')
            ->distinct()
            ->orderBy('month', 'desc')
            ->pluck('month')
            ->take(12); // Last 12 months
        
        return $months->map(function ($month) {
            $date = Carbon::createFromFormat('Y-m', $month);
            return [
                'value' => $month,
                'label' => $date->format('F Y')
            ];
        });
    }
}
