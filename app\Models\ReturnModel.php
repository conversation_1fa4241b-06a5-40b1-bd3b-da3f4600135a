<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ReturnModel extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'returns';

    protected $fillable = [
        'distribution_id',
        'product_id',
        'store_id',
        'supplier_id',
        'quantity',
        'reason',
        'description',
        'status',
        'return_date',
        'approved_date',
        'completed_date',
        'processed_date',
        'processing_action',
        'requested_by',
        'approved_by',
        'processed_by',
        'admin_notes',
        'processing_notes',
        'is_active',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'reason' => 'string',
        'status' => 'string',
        'processing_action' => 'string',
        'return_date' => 'date',
        'approved_date' => 'date',
        'completed_date' => 'date',
        'processed_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the distribution for the return.
     */
    public function distribution()
    {
        return $this->belongsTo(Distribution::class);
    }

    /**
     * Get the product for the return.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the store for the return (nullable for warehouse returns).
     */
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Get the supplier for the return (nullable if not returned to supplier).
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the user who requested the return.
     */
    public function requestedBy()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    /**
     * Get the admin who approved the return.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who processed the return.
     */
    public function processedBy()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Scope a query to only include requested returns.
     */
    public function scopeRequested($query)
    {
        return $query->where('status', 'requested');
    }

    /**
     * Scope a query to only include approved returns.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include in-transit returns.
     */
    public function scopeInTransit($query)
    {
        return $query->where('status', 'in_transit');
    }

    /**
     * Scope a query to only include completed returns.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include rejected returns.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope a query to only include store returns.
     */
    public function scopeFromStore($query)
    {
        return $query->whereNotNull('store_id');
    }

    /**
     * Scope a query to only include warehouse returns.
     */
    public function scopeFromWarehouse($query)
    {
        return $query->whereNull('store_id');
    }

    /**
     * Scope a query to only include returns to suppliers.
     */
    public function scopeToSupplier($query)
    {
        return $query->whereNotNull('supplier_id');
    }

    /**
     * Scope a query to only include active returns (not moved to history).
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include processed returns (moved to history).
     */
    public function scopeProcessed($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope a query to only include store-to-warehouse returns.
     */
    public function scopeStoreToWarehouse($query)
    {
        return $query->whereNotNull('store_id')->whereNull('supplier_id');
    }

    /**
     * Scope a query to only include warehouse-to-supplier returns.
     */
    public function scopeWarehouseToSupplier($query)
    {
        return $query->whereNull('store_id')->whereNotNull('supplier_id');
    }

    /**
     * Check if return is from store.
     */
    public function isFromStore()
    {
        return !is_null($this->store_id);
    }

    /**
     * Check if return is from warehouse.
     */
    public function isFromWarehouse()
    {
        return is_null($this->store_id);
    }

    /**
     * Check if return goes to supplier.
     */
    public function isToSupplier()
    {
        return !is_null($this->supplier_id);
    }

    /**
     * Get the return source (store name or warehouse).
     */
    public function getSourceAttribute()
    {
        return $this->isFromStore() ? $this->store->name : 'Gudang Pusat';
    }

    /**
     * Get the return destination (supplier name or warehouse).
     */
    public function getDestinationAttribute()
    {
        return $this->isToSupplier() ? $this->supplier->name : 'Gudang Pusat';
    }

    /**
     * Get the reason in Indonesian.
     */
    public function getReasonInIndonesianAttribute()
    {
        $reasons = [
            'damaged' => 'Rusak',
            'expired' => 'Kadaluarsa',
            'defective' => 'Cacat',
            'overstock' => 'Kelebihan Stok',
            'shortage' => 'Kekurangan Pengiriman',
            'other' => 'Lainnya',
        ];

        return $reasons[$this->reason] ?? $this->reason;
    }

    /**
     * Get the status in Indonesian.
     */
    public function getStatusInIndonesianAttribute()
    {
        $statuses = [
            'requested' => 'Diminta',
            'approved' => 'Disetujui',
            'in_transit' => 'Dalam Perjalanan',
            'completed' => 'Selesai',
            'rejected' => 'Ditolak',
            'processed' => 'Diproses',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get the processing action in Indonesian.
     */
    public function getProcessingActionInIndonesianAttribute()
    {
        if (!$this->processing_action) {
            return null;
        }

        $actions = [
            'deleted' => 'Dihapus',
            'resent' => 'Dikirim Ulang',
            'accepted' => 'Diterima',
            'rejected_final' => 'Ditolak Final',
        ];

        return $actions[$this->processing_action] ?? $this->processing_action;
    }

    /**
     * Move return to history with processing action.
     */
    public function moveToHistory($action, $notes = null, $processedBy = null)
    {
        $this->update([
            'is_active' => false,
            'status' => 'processed',
            'processing_action' => $action,
            'processed_date' => now(),
            'processed_by' => $processedBy ?? auth()->id(),
            'processing_notes' => $notes,
        ]);
    }

    /**
     * Check if return is in history.
     */
    public function isInHistory()
    {
        return !$this->is_active;
    }

    /**
     * Check if return is active.
     */
    public function isActive()
    {
        return $this->is_active;
    }
}
