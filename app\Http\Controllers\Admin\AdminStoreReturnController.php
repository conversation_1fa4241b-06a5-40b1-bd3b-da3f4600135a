<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\Store;
use App\Models\Supplier;
use App\Models\StoreStock;
use App\Models\StockMovement;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminStoreReturnController extends Controller
{
    /**
     * Display a listing of store-to-warehouse returns.
     */
    public function index(Request $request)
    {
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy'])
            ->active() // Only show active returns (not moved to history)
            ->storeToWarehouse(); // Only store-to-warehouse returns
        
        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);
        
        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();
        
        // Apply date filter
        $query->whereBetween('return_date', [$startDate, $endDate]);
        
        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('store', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }
        
        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // Apply store filter
        if ($request->filled('store_id')) {
            $query->where('store_id', $request->get('store_id'));
        }
        
        $returns = $query->orderBy('return_date', 'desc')->paginate(15);
        
        // Get statistics for store returns only
        $stats = [
            'total' => ReturnModel::whereNotNull('store_id')
                ->whereBetween('return_date', [$startDate, $endDate])->count(),
            'requested' => ReturnModel::whereNotNull('store_id')
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'requested')->count(),
            'approved' => ReturnModel::whereNotNull('store_id')
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'approved')->count(),
            'completed' => ReturnModel::whereNotNull('store_id')
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'completed')->count(),
            'forwarded_to_supplier' => ReturnModel::whereNotNull('store_id')
                ->whereNotNull('supplier_id')
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'in_transit')->count(),
        ];
        
        // Get stores for filter dropdown
        $stores = Store::orderBy('name')->get();
        
        return view('admin.store-returns.index', compact('returns', 'stats', 'filterMonth', 'stores'));
    }
    
    /**
     * Display the specified store return.
     */
    public function show(ReturnModel $storeReturn)
    {
        // Ensure this is a store return
        if (!$storeReturn->isFromStore()) {
            abort(404, 'Retur tidak ditemukan');
        }
        
        $storeReturn->load(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
        
        return view('admin.store-returns.show', compact('storeReturn'));
    }
    
    /**
     * Approve a store return request.
     */
    public function approve(Request $request, ReturnModel $storeReturn)
    {
        // Ensure this is a store return and status is requested
        if (!$storeReturn->isFromStore() || $storeReturn->status !== 'requested') {
            return redirect()->route('admin.store-returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        $storeReturn->update([
            'status' => 'approved',
            'approved_date' => now(),
            'approved_by' => auth()->id(),
            'admin_notes' => $validatedData['admin_notes'],
        ]);
        
        return redirect()->route('admin.store-returns.index')
            ->with('success', 'Retur dari toko berhasil disetujui');
    }
    
    /**
     * Reject a store return request.
     */
    public function reject(Request $request, ReturnModel $storeReturn)
    {
        // Ensure this is a store return and status is requested
        if (!$storeReturn->isFromStore() || $storeReturn->status !== 'requested') {
            return redirect()->route('admin.store-returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ], [
            'admin_notes.required' => 'Alasan penolakan wajib diisi',
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        $storeReturn->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'admin_notes' => $validatedData['admin_notes'],
        ]);

        // Move to history when rejected
        $storeReturn->moveToHistory('rejected_final', $validatedData['admin_notes'], auth()->id());

        return redirect()->route('admin.store-returns.index')
            ->with('success', 'Retur dari toko berhasil ditolak');
    }
    
    /**
     * Forward a store return to supplier.
     */
    public function forwardToSupplier(Request $request, ReturnModel $storeReturn)
    {
        // Only allow forwarding approved store returns
        if (!$storeReturn->isFromStore() || $storeReturn->status !== 'approved') {
            return redirect()->route('admin.store-returns.index')
                ->with('error', 'Retur ini tidak dapat diteruskan ke supplier');
        }
        
        $validatedData = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'supplier_id.required' => 'Supplier wajib dipilih',
            'supplier_id.exists' => 'Supplier tidak valid',
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        DB::transaction(function () use ($storeReturn, $validatedData) {
            // Update the return to forward to supplier
            $storeReturn->update([
                'supplier_id' => $validatedData['supplier_id'],
                'status' => 'in_transit',
                'admin_notes' => ($storeReturn->admin_notes ?? '') . "\n\nForwarded to supplier: " . ($validatedData['admin_notes'] ?? 'Diteruskan ke supplier'),
            ]);
            
            // Reduce store stock since it's being returned
            $storeStock = StoreStock::where('store_id', $storeReturn->store_id)
                ->where('product_id', $storeReturn->product_id)
                ->first();
            
            if ($storeStock && $storeStock->quantity >= $storeReturn->quantity) {
                $storeStock->decrement('quantity', $storeReturn->quantity);
                
                // Record stock movement
                StockMovement::create([
                    'product_id' => $storeReturn->product_id,
                    'type' => 'out',
                    'source' => 'return_to_supplier',
                    'quantity' => -$storeReturn->quantity,
                    'previous_stock' => $storeStock->quantity + $storeReturn->quantity,
                    'new_stock' => $storeStock->quantity,
                    'reference_type' => 'ReturnModel',
                    'reference_id' => $storeReturn->id,
                    'notes' => 'Retur toko diteruskan ke supplier: ' . $storeReturn->supplier->name,
                    'created_by' => auth()->id(),
                ]);
            }
        });
        
        return redirect()->route('admin.store-returns.index')
            ->with('success', 'Retur dari toko berhasil diteruskan ke supplier');
    }
    
    /**
     * Complete a store return (for returns not forwarded to supplier).
     */
    public function complete(Request $request, ReturnModel $storeReturn)
    {
        // Only allow completing approved store returns that are not forwarded to supplier
        if (!$storeReturn->isFromStore() || !in_array($storeReturn->status, ['approved', 'in_transit'])) {
            return redirect()->route('admin.store-returns.index')
                ->with('error', 'Retur ini tidak dapat diselesaikan');
        }
        
        $validatedData = $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ], [
            'admin_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        DB::transaction(function () use ($storeReturn, $validatedData) {
            // Update return status
            $storeReturn->update([
                'status' => 'completed',
                'completed_date' => now(),
                'admin_notes' => ($storeReturn->admin_notes ?? '') . "\n\nCompleted: " . ($validatedData['admin_notes'] ?? 'Retur selesai'),
            ]);

            // Move to history after completion
            $storeReturn->moveToHistory('accepted', $validatedData['admin_notes'] ?? 'Retur toko diselesaikan oleh admin', auth()->id());
            
            // If not forwarded to supplier, reduce store stock
            if (!$storeReturn->supplier_id) {
                $storeStock = StoreStock::where('store_id', $storeReturn->store_id)
                    ->where('product_id', $storeReturn->product_id)
                    ->first();
                
                if ($storeStock && $storeStock->quantity >= $storeReturn->quantity) {
                    $storeStock->decrement('quantity', $storeReturn->quantity);
                    
                    // Record stock movement
                    StockMovement::create([
                        'product_id' => $storeReturn->product_id,
                        'type' => 'out',
                        'source' => 'store_return_completed',
                        'quantity' => -$storeReturn->quantity,
                        'previous_stock' => $storeStock->quantity + $storeReturn->quantity,
                        'new_stock' => $storeStock->quantity,
                        'reference_type' => 'ReturnModel',
                        'reference_id' => $storeReturn->id,
                        'notes' => 'Retur toko diselesaikan: ' . $storeReturn->store->name,
                        'created_by' => auth()->id(),
                    ]);
                }
            }
        });
        
        return redirect()->route('admin.store-returns.index')
            ->with('success', 'Retur dari toko berhasil diselesaikan');
    }

    /**
     * Get the most recent supplier for a product (API endpoint).
     */
    public function getRecentSupplier(Product $product)
    {
        $recentDelivery = $product->supplierDeliveries()
            ->with('supplier')
            ->where('status', 'received')
            ->orderBy('received_date', 'desc')
            ->first();

        if ($recentDelivery) {
            return response()->json([
                'supplier' => [
                    'id' => $recentDelivery->supplier->id,
                    'name' => $recentDelivery->supplier->name,
                ],
                'last_delivery_date' => $recentDelivery->received_date->format('d/m/Y'),
            ]);
        }

        return response()->json(['supplier' => null]);
    }
}
