<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Dashboard Supplier - Indah Berkah Abadi'); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Supplier Dashboard Specific Styles -->
    <link rel="stylesheet" href="<?php echo e(asset('css/supplier-dashboard.css')); ?>?v=<?php echo e(time()); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/supplier-dashboard-mobile-fixes.css')); ?>?v=<?php echo e(time()); ?>">









    <!-- Additional Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="supplier-dashboard-layout">
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="supplier-dashboard-sidebar-overlay" onclick="closeSidebar()"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="supplier-dashboard-sidebar">
        <!-- Logo Section -->
        <div class="supplier-dashboard-sidebar-header">
            <div class="supplier-dashboard-sidebar-brand">
                <div class="supplier-dashboard-logo-icon">IBA</div>
                <div class="supplier-dashboard-logo-text">
                    <div class="supplier-dashboard-logo-title">Indah Berkah Abadi</div>
                    <div class="supplier-dashboard-logo-subtitle">Dashboard Supplier</div>
                </div>
            </div>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="supplier-dashboard-nav">
            <a href="<?php echo e(route('supplier.dashboard')); ?>" class="supplier-dashboard-nav-item <?php echo e(request()->routeIs('supplier.dashboard') ? 'supplier-dashboard-nav-item-active' : ''); ?>">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 5 4-4 4 4"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Dashboard</span>
            </a>

            <a href="<?php echo e(route('supplier.deliveries.index')); ?>" class="supplier-dashboard-nav-item <?php echo e(request()->routeIs('supplier.deliveries.*') ? 'supplier-dashboard-nav-item-active' : ''); ?>">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Pengiriman</span>
            </a>

            <a href="<?php echo e(route('supplier.returns.index')); ?>" class="supplier-dashboard-nav-item <?php echo e(request()->routeIs('supplier.returns.*') && !request()->routeIs('supplier.return-history.*') ? 'supplier-dashboard-nav-item-active' : ''); ?>">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Retur</span>
            </a>

            <a href="<?php echo e(route('supplier.return-history.index')); ?>" class="supplier-dashboard-nav-item <?php echo e(request()->routeIs('supplier.return-history.*') ? 'supplier-dashboard-nav-item-active' : ''); ?>">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Riwayat Retur</span>
            </a>

            <a href="<?php echo e(route('supplier.profile.show')); ?>" class="supplier-dashboard-nav-item <?php echo e(request()->routeIs('supplier.profile.*') ? 'supplier-dashboard-nav-item-active' : ''); ?>">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Profil</span>
            </a>
        </nav>
        
        <!-- Sidebar Footer with Profile -->
        <div class="supplier-dashboard-sidebar-footer">
            <div class="supplier-dashboard-user-menu">
                <button class="supplier-dashboard-user-menu-button" id="userMenuButton" type="button" aria-expanded="false" aria-haspopup="true">
                    <div class="supplier-dashboard-user-info">
                        <div class="supplier-dashboard-user-avatar">
                            <span class="supplier-dashboard-user-initial"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                        </div>
                        <div class="supplier-dashboard-user-details">
                            <p class="supplier-dashboard-user-name"><?php echo e(auth()->user()->name); ?></p>
                            <p class="supplier-dashboard-user-role">Supplier Admin</p>
                        </div>
                    </div>
                    <svg class="supplier-dashboard-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="supplier-dashboard-user-menu-dropdown" id="userMenuDropdown" role="menu" aria-labelledby="user-menu-button">
                    <a href="<?php echo e(route('supplier.profile.show')); ?>" class="supplier-dashboard-dropdown-item" role="menuitem">
                        <svg class="supplier-dashboard-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Lihat Profil
                    </a>
                    <div class="supplier-dashboard-dropdown-separator" role="separator"></div>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="supplier-dashboard-logout-form">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="supplier-dashboard-dropdown-item supplier-dashboard-logout-button" role="menuitem">
                            <svg class="supplier-dashboard-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            Keluar
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="supplier-dashboard-main">
        <!-- Header -->
        <header class="supplier-dashboard-header">
            <div class="supplier-dashboard-header-left">
                <button class="supplier-dashboard-mobile-menu-btn" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>

                <div class="supplier-dashboard-breadcrumb">
                    <h1 class="supplier-dashboard-page-title"><?php echo $__env->yieldContent('page-title', 'Dashboard Supplier'); ?></h1>
                </div>
            </div>

            <div class="supplier-dashboard-header-right">
                <!-- User Dropdown -->
                <div class="supplier-dashboard-user-dropdown">
                    <button class="supplier-dashboard-user-dropdown-btn" onclick="toggleUserMenu()">
                        <div class="supplier-dashboard-user-avatar-small">
                            <?php echo e(substr(auth()->user()->name, 0, 1)); ?>

                        </div>
                        <span class="supplier-dashboard-user-name-small"><?php echo e(auth()->user()->name); ?></span>
                        <svg class="supplier-dashboard-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <div id="user-dropdown-menu" class="supplier-dashboard-user-dropdown-menu">
                        <a href="<?php echo e(route('supplier.profile.show')); ?>" class="supplier-dashboard-dropdown-item">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Profil Saya
                        </a>
                        <div class="supplier-dashboard-dropdown-divider"></div>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="supplier-dashboard-dropdown-item supplier-dashboard-dropdown-item-danger">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Keluar
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Content Area -->
        <div class="supplier-dashboard-content">
            <!-- Flash Messages -->
            <?php if(session('success')): ?>
                <div class="supplier-dashboard-alert supplier-dashboard-alert-success">
                    <svg class="supplier-dashboard-alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span><?php echo e(session('success')); ?></span>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="supplier-dashboard-alert supplier-dashboard-alert-error">
                    <svg class="supplier-dashboard-alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span><?php echo e(session('error')); ?></span>
                </div>
            <?php endif; ?>

            <?php if(session('info')): ?>
                <div class="supplier-dashboard-alert supplier-dashboard-alert-info">
                    <svg class="supplier-dashboard-alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span><?php echo e(session('info')); ?></span>
                </div>
            <?php endif; ?>

            <!-- Main Content -->
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </main>
    
    <!-- JavaScript Files -->
    <!-- Note: app.js is loaded via @vite directive in head section -->

    <!-- Supplier Dashboard JavaScript -->
    <script src="<?php echo e(asset('js/supplier-dashboard.js')); ?>?v=<?php echo e(time()); ?>"></script>

    <script>
        // Simple initialization - main functionality is in supplier-dashboard.js
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Supplier dashboard layout loaded');

            // Debug function for troubleshooting
            if (typeof window.debugSupplierDashboard === 'function') {
                console.log('Debug function available - call debugSupplierDashboard() in console');
            }
        });
    </script>

    <!-- Additional Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/layouts/supplier.blade.php ENDPATH**/ ?>